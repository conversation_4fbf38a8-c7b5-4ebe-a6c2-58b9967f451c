<script>
    $(document).ready(function() {
        // Revenue Breakdown Donut Chart
        if (document.querySelector('#revenue-breakdown-chart')) {
            var revenueValues = <?php echo json_encode($revenues('value'), 15, 512) ?>;
            var revenueColors = <?php echo json_encode($revenues('color'), 15, 512) ?>;
            var revenueLabels = <?php echo json_encode($revenues('label'), 15, 512) ?>;

            // Validate data and filter out invalid values
            var validData = [];
            var validColors = [];
            var validLabels = [];

            for (var i = 0; i < revenueValues.length; i++) {
                var value = parseFloat(revenueValues[i]);
                if (!isNaN(value) && isFinite(value) && value > 0) {
                    validData.push(value);
                    validColors.push(revenueColors[i] || '#6b7280');
                    validLabels.push(revenueLabels[i] || 'Unknown');
                }
            }

            if (validData.length > 0) {
                new ApexCharts(document.querySelector('#revenue-breakdown-chart'), {
                    series: validData,
                    colors: validColors,
                    chart: {
                        height: '250',
                        type: 'donut'
                    },
                    labels: validLabels,
                plotOptions: {
                    pie: {
                        donut: {
                            size: '71%',
                            polygons: {
                                strokeWidth: 0
                            }
                        },
                        expandOnClick: true
                    }
                },
                states: {
                    hover: {
                        filter: {
                            type: 'darken',
                            value: .9
                        }
                    }
                },
                dataLabels: {
                    enabled: false
                },
                legend: {
                    show: false
                },
                tooltip: {
                    enabled: false
                }
            }).render();
            } else {
                // Show no data message if no valid data
                document.querySelector('#revenue-breakdown-chart').innerHTML = '<div class="text-center p-4"><p><?php echo e(trans('core/base::tables.no_data')); ?></p></div>';
            }
        }

        // Financial Report Area Chart
        if (document.querySelector('#financial-report-chart')) {
            var salesReportSeries = <?php echo json_encode($salesReport['series'], 15, 512) ?>;
            var salesReportColors = <?php echo json_encode($salesReport['colors'], 15, 512) ?>;
            var salesReportDates = <?php echo json_encode($salesReport['dates'], 15, 512) ?>;

            // Validate and clean series data
            var validSeries = [];
            for (var i = 0; i < salesReportSeries.length; i++) {
                var series = salesReportSeries[i];
                var validData = [];

                for (var j = 0; j < series.data.length; j++) {
                    var value = parseFloat(series.data[j]);
                    validData.push(isNaN(value) || !isFinite(value) ? 0 : value);
                }

                validSeries.push({
                    name: series.name,
                    data: validData
                });
            }

            if (validSeries.length > 0 && salesReportDates.length > 0) {
                // Special handling for single date scenarios
                var chartConfig = {
                    series: validSeries,
                    chart: {
                        height: 350,
                        type: 'area',
                        toolbar: {
                            show: false
                        }
                    },
                    dataLabels: {
                        enabled: false
                    },
                    stroke: {
                        curve: 'smooth'
                    },
                    colors: salesReportColors,
                    xaxis: {
                        type: 'datetime',
                        categories: salesReportDates
                    },
                    tooltip: {
                        x: {
                            format: 'dd/MM/yy'
                        }
                    },
                    noData: {
                        text: '<?php echo e(trans('core/base::tables.no_data')); ?>',
                    }
                };

                // If only one date, adjust chart type and configuration
                if (salesReportDates.length === 1) {
                    chartConfig.chart.type = 'bar';
                    chartConfig.plotOptions = {
                        bar: {
                            horizontal: false,
                            columnWidth: '50%',
                            endingShape: 'rounded'
                        }
                    };
                    chartConfig.xaxis.type = 'category';
                    chartConfig.xaxis.categories = [salesReportDates[0]];
                }

                new ApexCharts(document.querySelector('#financial-report-chart'), chartConfig).render();
            } else {
                // Show no data message if no valid data
                document.querySelector('#financial-report-chart').innerHTML = '<div class="text-center p-4"><p><?php echo e(trans('core/base::tables.no_data')); ?></p></div>';
            }
        }
    });
</script>
<?php /**PATH D:\laragon\www\shofy\platform/plugins/expense-calculations/resources/views/reports/widgets/chart-script.blade.php ENDPATH**/ ?>